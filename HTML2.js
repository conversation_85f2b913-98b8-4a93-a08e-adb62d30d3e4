// Improved Reddit HTML Capture Bookmarklet
// This version handles lazy loading and infinite scroll

javascript:(function(){
    let isScrolling = false;
    let scrollAttempts = 0;
    const maxScrollAttempts = 50; // Increased for more content

    function getRandomDelay() {
        // Random delay between 3-5 seconds (3000-5000ms)
        return Math.floor(Math.random() * 2000) + 3000;
    }
    
    // Show progress indicator
    const progressDiv = document.createElement('div');
    progressDiv.style.cssText = `
        position: fixed; 
        top: 10px; 
        right: 10px; 
        background: #ff4500; 
        color: white; 
        padding: 10px; 
        border-radius: 5px; 
        z-index: 10000; 
        font-family: Arial; 
        font-size: 14px;
    `;
    progressDiv.textContent = 'Loading Reddit content... 0%';
    document.body.appendChild(progressDiv);
    
    function updateProgress(current, total) {
        const percent = Math.round((current / total) * 100);
        progressDiv.textContent = `Loading Reddit content... ${percent}% (${current}/${total})`;
    }
    
    function scrollToLoadContent() {
        return new Promise((resolve) => {
            if (scrollAttempts >= maxScrollAttempts) {
                resolve();
                return;
            }
            
            scrollAttempts++;
            updateProgress(scrollAttempts, maxScrollAttempts);

            // Scroll to bottom to trigger lazy loading
            window.scrollTo(0, document.body.scrollHeight);

            // Use random delay between 3-5 seconds
            const delay = getRandomDelay();
            console.log(`Scroll ${scrollAttempts}/${maxScrollAttempts}, waiting ${delay}ms`);

            // Wait for content to load
            setTimeout(() => {
                // Check if new content was loaded by comparing post count
                const currentPosts = document.querySelectorAll('shreddit-post, [data-testid="post-container"], article').length;
                console.log(`Found ${currentPosts} posts after scroll ${scrollAttempts}`);

                // Continue scrolling
                scrollToLoadContent().then(resolve);
            }, delay);
        });
    }
    
    function captureHTML() {
        // Scroll back to top
        window.scrollTo(0, 0);
        
        // Wait a moment for any final loading
        setTimeout(() => {
            const html = document.documentElement.outerHTML;
            
            function fallback() {
                const ta = document.createElement('textarea');
                ta.value = html;
                document.body.appendChild(ta);
                ta.select();
                document.execCommand('copy');
                document.body.removeChild(ta);
            }
            
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(html).then(() => {
                    progressDiv.textContent = 'Reddit HTML copied to clipboard!';
                    progressDiv.style.background = '#28a745';
                    setTimeout(() => document.body.removeChild(progressDiv), 3000);
                }, () => {
                    fallback();
                    progressDiv.textContent = 'Reddit HTML copied via fallback!';
                    progressDiv.style.background = '#28a745';
                    setTimeout(() => document.body.removeChild(progressDiv), 3000);
                });
            } else {
                fallback();
                progressDiv.textContent = 'Reddit HTML copied via fallback!';
                progressDiv.style.background = '#28a745';
                setTimeout(() => document.body.removeChild(progressDiv), 3000);
            }
        }, 1000);
    }
    
    // Start the loading process
    scrollToLoadContent().then(() => {
        progressDiv.textContent = 'Capturing HTML...';
        captureHTML();
    });
})();
