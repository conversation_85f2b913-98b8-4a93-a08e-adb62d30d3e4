javascript:(function(){let a=0,b=0;const c=50,e=document.createElement('div');e.style.cssText='position:fixed;top:10px;right:10px;background:#ff4500;color:white;padding:10px;border-radius:5px;z-index:10000;font-family:Arial;font-size:14px;',e.textContent='Loading Reddit content... 0%',document.body.appendChild(e);function f(){return Math.floor(Math.random()*2000)+3000}function g(h,i){const j=Math.round(h/i*100);e.textContent=`Loading Reddit content... ${j}% (${h}/${i})`}function k(){return new Promise(h=>{if(b>=c){h();return}b++,g(b,c),window.scrollTo(0,document.body.scrollHeight);const i=f();console.log(`Scroll ${b}/${c}, waiting ${i}ms`),setTimeout(()=>{const j=document.querySelectorAll('shreddit-post, [data-testid="post-container"], article').length;console.log(`Found ${j} posts after scroll ${b}`),k().then(h)},i)})}function l(){window.scrollTo(0,0),setTimeout(()=>{const h=document.documentElement.outerHTML;function i(){const j=document.createElement('textarea');j.value=h,document.body.appendChild(j),j.select(),document.execCommand('copy'),document.body.removeChild(j)}navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(h).then(()=>{e.textContent='Reddit HTML copied to clipboard!',e.style.background='#28a745',setTimeout(()=>document.body.removeChild(e),3000)},()=>{i(),e.textContent='Reddit HTML copied via fallback!',e.style.background='#28a745',setTimeout(()=>document.body.removeChild(e),3000)}):(i(),e.textContent='Reddit HTML copied via fallback!',e.style.background='#28a745',setTimeout(()=>document.body.removeChild(e),3000))},1000)}k().then(()=>{e.textContent='Capturing HTML...',l()})})();
